import React, { useState, useEffect, ReactNode } from 'react';
import { Eye, Check, X, Calendar, Plus, Search, Upload, File, Trash2 } from 'lucide-react';

// Types and Interfaces
enum LeaveStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

interface LeaveRequest {
  id: string;
  employeeId: number;
  employeeName: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  reason: string;
  isHalfDay: boolean;
  status: LeaveStatus;
  daysRequested: number;
  emergencyContact?: string;
  workCoverage?: string;
  attachments?: File[];
  requestDate: string;
  createdBy: string;
}

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: Array<{
    leaveType: string;
    totalDays: number;
    usedDays: number;
    remainingDays: number;
  }>;
  recentRequests: LeaveRequest[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface LeaveType {
  value: string;
  label: string;
  maxDays?: number;
  requiresApproval?: boolean;
  color?: string;
}

interface LeaveRequestsTableProps {
  paginatedRequests: LeaveRequest[];
  employeeLeaveData: EmployeeLeaveData[];
  onApprove: (request: LeaveRequest) => void;
  onReject: (request: LeaveRequest) => void;
  onViewDetails: (request: LeaveRequest) => void;
  onCreateRequest?: (request: Omit<LeaveRequest, 'id'>) => Promise<void>;
  filtersBar: ReactNode;
  title?: string;
  showAddButton?: boolean;
  leaveTypes: LeaveType[];
}

const LeaveRequestsTable: React.FC<LeaveRequestsTableProps> = ({
  paginatedRequests,
  employeeLeaveData,
  onApprove,
  onReject,
  onViewDetails,
  onCreateRequest,
  filtersBar,
  title = "All Leave Requests",
  showAddButton = true,
  leaveTypes = []
}) => {
  // Modal state and form fields
  const [showAddLeaveModal, setShowAddLeaveModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [employeeSearch, setEmployeeSearch] = useState('');
  const [leaveType, setLeaveType] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isHalfDay, setIsHalfDay] = useState(false);
  const [reason, setReason] = useState('');
  const [emergencyContact, setEmergencyContact] = useState('');
  const [workCoverage, setWorkCoverage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState<{type: 'success' | 'error', message: string} | null>(null);

  // Filter employees based on search
  const filteredEmployees = employeeLeaveData.filter(employee =>
    employee.employeeName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.employeeCode.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.department.toLowerCase().includes(employeeSearch.toLowerCase())
  );

  // Get selected employee data for leave balance info
  const selectedEmployeeData = selectedEmployee 
    ? employeeLeaveData.find(emp => emp.employeeId.toString() === selectedEmployee)
    : null;

  // Calculate available leave days for selected leave type
  const getAvailableLeaveDays = () => {
    if (!selectedEmployeeData || !leaveType) return null;
    
    const leaveBalance = (selectedEmployeeData.leaveBalances || []).find(
      balance => balance.leaveType === leaveType
    );
    
    return leaveBalance ? leaveBalance.remainingDays : 0;
  };

  // Handle file upload
  const handleFileUpload = (files: FileList) => {
    const newFiles = Array.from(files).filter(file => {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setNotification({ type: 'error', message: `File "${file.name}" is too large. Maximum size is 10MB.` });
        return false;
      }
      
      // Check file type
      const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.txt'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!allowedTypes.includes(fileExtension)) {
        setNotification({ type: 'error', message: `File "${file.name}" has unsupported format.` });
        return false;
      }
      
      // Check if file already exists
      if (attachments.some(existing => existing.name === file.name && existing.size === file.size)) {
        setNotification({ type: 'error', message: `File "${file.name}" is already selected.` });
        return false;
      }
      
      return true;
    });
    
    setAttachments(prev => [...prev, ...newFiles]);
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  // Remove file
  const removeFile = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Calculate days between dates
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  // Reset form
  const resetForm = () => {
    setSelectedEmployee('');
    setEmployeeSearch('');
    setLeaveType('');
    setStartDate('');
    setEndDate('');
    setIsHalfDay(false);
    setReason('');
    setEmergencyContact('');
    setWorkCoverage('');
    setAttachments([]);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedEmployee || !leaveType || !startDate || !endDate || !reason) {
      setNotification({ type: 'error', message: 'Please fill all required fields.' });
      return;
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (start < today) {
      setNotification({ type: 'error', message: 'Start date cannot be in the past.' });
      return;
    }

    if (start > end) {
      setNotification({ type: 'error', message: 'End date must be after start date.' });
      return;
    }

    // Check leave balance
    const requestedDays = isHalfDay ? 0.5 : calculateDays(startDate, endDate);
    const availableDays = getAvailableLeaveDays();
    
    if (availableDays !== null && requestedDays > availableDays) {
      setNotification({ 
        type: 'error', 
        message: `Insufficient leave balance. Available: ${availableDays} days, Requested: ${requestedDays} days.` 
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const selectedEmp = employeeLeaveData.find(emp => emp.employeeId.toString() === selectedEmployee);
      
      const leaveRequest: Omit<LeaveRequest, 'id'> = {
        employeeId: parseInt(selectedEmployee),
        employeeName: selectedEmp?.employeeName || '',
        leaveType: leaveType,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
        isHalfDay: isHalfDay,
        status: LeaveStatus.PENDING,
        daysRequested: requestedDays,
        emergencyContact: emergencyContact || undefined,
        workCoverage: workCoverage || undefined,
        attachments: attachments.length > 0 ? attachments : undefined,
        requestDate: new Date().toISOString().split('T')[0],
        createdBy: 'HR Admin'
      };

      if (onCreateRequest) {
        await onCreateRequest(leaveRequest);
      } else {
        // Fallback simulation
        console.log('Creating leave request:', leaveRequest);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      setNotification({ type: 'success', message: 'Leave request created successfully!' });
      resetForm();
      setShowAddLeaveModal(false);
      
    } catch (error: any) {
      setNotification({ type: 'error', message: error.message || 'Failed to create leave request.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-dismiss notifications
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => setNotification(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);

  // Get status badge
  const getStatusBadge = (status: LeaveStatus) => {
    const statusStyles = {
      [LeaveStatus.PENDING]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      [LeaveStatus.APPROVED]: 'bg-green-100 text-green-800 border-green-200',
      [LeaveStatus.REJECTED]: 'bg-red-100 text-red-800 border-red-200',
      [LeaveStatus.CANCELLED]: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full border ${statusStyles[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Get leave type label
  const getLeaveTypeLabel = (leaveType: string) => {
    const typeObj = (leaveTypes || []).find(type => type.value === leaveType);
    return typeObj ? typeObj.label : leaveType;
  };

  // Empty state
  if (paginatedRequests.length === 0) {
    return (
      <>
        {/* Header with title and button */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
          {showAddButton && (
            <button
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
              onClick={() => setShowAddLeaveModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Leave Request
            </button>
          )}
        </div>

        <div className="mb-6">{filtersBar}</div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No leave requests found</h3>
          <p className="text-gray-500 mb-6">
            No requests match your current filters. Try adjusting your search criteria.
          </p>
          {showAddButton && (
            <button
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              onClick={() => setShowAddLeaveModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create First Request
            </button>
          )}
        </div>

        {/* Add Leave Modal */}
        {showAddLeaveModal && (
          <AddLeaveModal
            isOpen={showAddLeaveModal}
            onClose={() => setShowAddLeaveModal(false)}
            employeeLeaveData={employeeLeaveData}
            filteredEmployees={filteredEmployees}
            leaveTypes={leaveTypes}
            selectedEmployee={selectedEmployee}
            setSelectedEmployee={setSelectedEmployee}
            employeeSearch={employeeSearch}
            setEmployeeSearch={setEmployeeSearch}
            leaveType={leaveType}
            setLeaveType={setLeaveType}
            startDate={startDate}
            setStartDate={setStartDate}
            endDate={endDate}
            setEndDate={setEndDate}
            isHalfDay={isHalfDay}
            setIsHalfDay={setIsHalfDay}
            reason={reason}
            setReason={setReason}
            emergencyContact={emergencyContact}
            setEmergencyContact={setEmergencyContact}
            workCoverage={workCoverage}
            setWorkCoverage={setWorkCoverage}
            attachments={attachments}
            handleFileUpload={handleFileUpload}
            handleDragOver={handleDragOver}
            handleDrop={handleDrop}
            removeFile={removeFile}
            formatFileSize={formatFileSize}
            isSubmitting={isSubmitting}
            onSubmit={handleSubmit}
            notification={notification}
            calculateDays={calculateDays}
            selectedEmployeeData={selectedEmployeeData ?? null}
            getAvailableLeaveDays={getAvailableLeaveDays}
          />
        )}
      </>
    );
  }

  return (
    <>
      {/* Header with title and button */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
        {showAddButton && (
          <button
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            onClick={() => setShowAddLeaveModal(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Leave Request
          </button>
        )}
      </div>

      <div className="mb-6">{filtersBar}</div>
      
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Leave Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                  Dates
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedRequests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-white">
                          {request.employeeName.split(' ').map((n: string) => n[0]).join('').slice(0, 2)}
                        </span>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {request.employeeName}
                        </div>
                        <div className="text-xs text-gray-500">
                          ID: {request.employeeId}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {getLeaveTypeLabel(request.leaveType)}
                    </div>
                    {request.isHalfDay && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        Half Day
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {request.daysRequested} {request.daysRequested === 1 ? 'day' : 'days'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                    <div>
                      {new Date(request.startDate).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric',
                        year: new Date(request.startDate).getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
                      })}
                      {request.startDate !== request.endDate && (
                        <>
                          {' - '}
                          {new Date(request.endDate).toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric',
                            year: new Date(request.endDate).getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
                          })}
                        </>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 hidden lg:table-cell">
                    <div className="text-sm text-gray-900 max-w-xs truncate" title={request.reason}>
                      {request.reason}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(request.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => onViewDetails(request)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-colors" 
                        title="View Details"
                        aria-label={`View details for ${request.employeeName}'s leave request`}
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {request.status === LeaveStatus.PENDING && (
                        <>
                          <button 
                            onClick={() => onApprove(request)}
                            className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50 transition-colors"
                            title="Approve"
                            aria-label={`Approve leave request for ${request.employeeName}`}
                          >
                            <Check className="h-4 w-4" />
                          </button>
                          <button 
                            onClick={() => onReject(request)}
                            className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                            title="Reject"
                            aria-label={`Reject leave request for ${request.employeeName}`}
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Leave Modal */}
      {showAddLeaveModal && (
        <AddLeaveModal
          isOpen={showAddLeaveModal}
          onClose={() => setShowAddLeaveModal(false)}
          employeeLeaveData={employeeLeaveData}
          filteredEmployees={filteredEmployees}
          leaveTypes={leaveTypes}
          selectedEmployee={selectedEmployee}
          setSelectedEmployee={setSelectedEmployee}
          employeeSearch={employeeSearch}
          setEmployeeSearch={setEmployeeSearch}
          leaveType={leaveType}
          setLeaveType={setLeaveType}
          startDate={startDate}
          setStartDate={setStartDate}
          endDate={endDate}
          setEndDate={setEndDate}
          isHalfDay={isHalfDay}
          setIsHalfDay={setIsHalfDay}
          reason={reason}
          setReason={setReason}
          emergencyContact={emergencyContact}
          setEmergencyContact={setEmergencyContact}
          workCoverage={workCoverage}
          setWorkCoverage={setWorkCoverage}
          attachments={attachments}
          handleFileUpload={handleFileUpload}
          handleDragOver={handleDragOver}
          handleDrop={handleDrop}
          removeFile={removeFile}
          formatFileSize={formatFileSize}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmit}
          notification={notification}
          calculateDays={calculateDays}
          selectedEmployeeData={selectedEmployeeData ?? null}
          getAvailableLeaveDays={getAvailableLeaveDays}
        />
      )}

      {/* Global notification */}
      {notification && (
        <div className="fixed top-4 right-4 z-50 max-w-sm">
          <div className={`p-4 rounded-lg shadow-lg border ${
            notification.type === 'success' 
              ? 'bg-green-50 text-green-800 border-green-200' 
              : 'bg-red-50 text-red-800 border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {notification.type === 'success' ? (
                  <Check className="h-5 w-5 text-green-400" />
                ) : (
                  <X className="h-5 w-5 text-red-400" />
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{notification.message}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setNotification(null)}
                  className="inline-flex text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

// Modal Component
interface AddLeaveModalProps {
  isOpen: boolean;
  onClose: () => void;
  employeeLeaveData: EmployeeLeaveData[];
  filteredEmployees: EmployeeLeaveData[];
  leaveTypes: LeaveType[];
  selectedEmployee: string;
  setSelectedEmployee: (value: string) => void;
  employeeSearch: string;
  setEmployeeSearch: (value: string) => void;
  leaveType: string;
  setLeaveType: (value: string) => void;
  startDate: string;
  setStartDate: (value: string) => void;
  endDate: string;
  setEndDate: (value: string) => void;
  isHalfDay: boolean;
  setIsHalfDay: (value: boolean) => void;
  reason: string;
  setReason: (value: string) => void;
  emergencyContact: string;
  setEmergencyContact: (value: string) => void;
  workCoverage: string;
  setWorkCoverage: (value: string) => void;
  attachments: File[];
  handleFileUpload: (files: FileList) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDrop: (e: React.DragEvent) => void;
  removeFile: (index: number) => void;
  formatFileSize: (bytes: number) => string;
  isSubmitting: boolean;
  onSubmit: () => void;
  notification: {type: 'success' | 'error', message: string} | null;
  calculateDays: (start: string, end: string) => number;
  selectedEmployeeData: EmployeeLeaveData | null;
  getAvailableLeaveDays: () => number | null;
}

const AddLeaveModal: React.FC<AddLeaveModalProps> = ({
  isOpen,
  onClose,
  employeeLeaveData,
  filteredEmployees,
  leaveTypes,
  selectedEmployee,
  setSelectedEmployee,
  employeeSearch,
  setEmployeeSearch,
  leaveType,
  setLeaveType,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  isHalfDay,
  setIsHalfDay,
  reason,
  setReason,
  emergencyContact,
  setEmergencyContact,
  workCoverage,
  setWorkCoverage,
  attachments,
  handleFileUpload,
  handleDragOver,
  handleDrop,
  removeFile,
  formatFileSize,
  isSubmitting,
  onSubmit,
  notification,
  calculateDays,
  selectedEmployeeData,
  getAvailableLeaveDays
}) => {
  if (!isOpen) return null;

  const totalDays = startDate && endDate ? (isHalfDay ? 0.5 : calculateDays(startDate, endDate)) : 0;
  const availableDays = getAvailableLeaveDays();
  const selectedLeaveType = (leaveTypes || []).find(lt => lt.value === leaveType);

  // Inline validation
  const [touched, setTouched] = React.useState<{[key:string]: boolean}>({});
  const showError = (field: string) => touched[field] && !eval(field);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-5xl max-h-[95vh] overflow-y-auto shadow-2xl border border-gray-200">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-6 rounded-t-2xl flex items-center justify-between">
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Add Leave Request</h3>
            <p className="text-gray-600 text-sm mt-1">Create a new leave request for an employee</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isSubmitting}
            aria-label="Close modal"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        <div className="p-8 space-y-8">
          {/* Employee Selection Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <section className="bg-gray-50 rounded-xl p-6 shadow-sm border col-span-1">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center text-lg">
                <Search className="h-5 w-5 mr-2 text-gray-600" />
                Employee Selection
              </h4>
              {/* Employee Search */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Employee
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Search by name, code, or department..."
                    value={employeeSearch}
                    onChange={e => setEmployeeSearch(e.target.value)}
                  />
                </div>
                {employeeSearch && (
                  <p className="text-xs text-gray-500 mt-1">
                    {filteredEmployees.length} employee(s) found
                  </p>
                )}
              </div>
              {/* Employee Selection */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SELECT EMPLOYEE <span className="text-red-500">*</span>
                </label>
                <select
                  className={`w-full border ${showError('selectedEmployee') ? 'border-red-400' : 'border-gray-300'} rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                  value={selectedEmployee}
                  onChange={e => { setSelectedEmployee(e.target.value); setTouched(t => ({...t, selectedEmployee: true})); }}
                  onBlur={() => setTouched(t => ({...t, selectedEmployee: true}))}
                  required
                >
                  <option value="">Select Employee</option>
                  {(filteredEmployees || []).map((employee) => (
                    <option key={employee.employeeId} value={employee.employeeId.toString()}>
                      {employee.employeeName} ({employee.employeeCode})
                    </option>
                  ))}
                </select>
                {showError('selectedEmployee') && (
                  <p className="text-xs text-red-600 mt-1">Employee is required.</p>
                )}
              </div>
              {/* Employee Details */}
              {selectedEmployeeData && (
                <div className="bg-white border border-gray-200 rounded-lg p-4 mt-4 shadow-sm">
                  <h5 className="font-medium text-gray-900 mb-3 flex items-center">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                      <span className="text-xs font-medium text-blue-600">
                        {selectedEmployeeData.employeeName.split(' ').map(n => n[0]).join('').slice(0, 2)}
                      </span>
                    </div>
                    Employee Details
                  </h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Department:</span>
                      <span className="font-medium text-gray-900">{selectedEmployeeData.department}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Position:</span>
                      <span className="font-medium text-gray-900">{selectedEmployeeData.position}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Days Used:</span>
                      <span className="font-medium text-gray-900">{selectedEmployeeData.totalDaysUsed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Days Remaining:</span>
                      <span className="font-medium text-green-600">{selectedEmployeeData.totalDaysRemaining}</span>
                    </div>
                  </div>
                  {/* Leave Type Balances */}
                  {(selectedEmployeeData.leaveBalances || []).length > 0 && (
                    <div className="mt-4 pt-3 border-t border-gray-200">
                      <h6 className="text-xs font-medium text-gray-700 mb-2">LEAVE BALANCES</h6>
                      <div className="space-y-1">
                        {(selectedEmployeeData.leaveBalances || []).map((balance, index) => (
                          <div key={index} className="flex justify-between text-xs">
                            <span className="text-gray-600">{balance.leaveType}:</span>
                            <span className="font-medium">{balance.remainingDays}/{balance.totalDays}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </section>
            {/* Leave Details Section */}
            <section className="col-span-2">
              <div className="bg-white rounded-xl p-6 shadow-sm border mb-8">
                <h4 className="font-semibold text-gray-900 mb-6 flex items-center text-lg">
                  <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                  Leave Details
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Leave Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      LEAVE TYPE <span className="text-red-500">*</span>
                    </label>
                    <select
                      className={`w-full border ${showError('leaveType') ? 'border-red-400' : 'border-gray-300'} rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                      value={leaveType}
                      onChange={e => { setLeaveType(e.target.value); setTouched(t => ({...t, leaveType: true})); }}
                      onBlur={() => setTouched(t => ({...t, leaveType: true}))}
                      required
                    >
                      <option value="">Select Leave Type</option>
                      {(leaveTypes || []).map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                          {type.maxDays && ` (Max: ${type.maxDays} days)`}
                        </option>
                      ))}
                    </select>
                    {showError('leaveType') && (
                      <p className="text-xs text-red-600 mt-1">Leave type is required.</p>
                    )}
                    {selectedLeaveType?.requiresApproval && (
                      <p className="text-xs text-amber-600 mt-1">⚠️ This leave type requires supervisor approval</p>
                    )}
                  </div>
                  {/* Half Day Toggle */}
                  <div className="flex items-center justify-center">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="halfDay"
                        checked={isHalfDay}
                        onChange={e => setIsHalfDay(e.target.checked)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 transition-colors"
                      />
                      <label htmlFor="halfDay" className="text-sm font-medium text-gray-700">
                        Half Day Leave
                      </label>
                    </div>
                  </div>
                  {/* Start Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      START DATE <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      className={`w-full border ${showError('startDate') ? 'border-red-400' : 'border-gray-300'} rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                      value={startDate}
                      onChange={e => { setStartDate(e.target.value); setTouched(t => ({...t, startDate: true})); }}
                      onBlur={() => setTouched(t => ({...t, startDate: true}))}
                      min={new Date().toISOString().split('T')[0]}
                      required
                    />
                    {showError('startDate') && (
                      <p className="text-xs text-red-600 mt-1">Start date is required.</p>
                    )}
                  </div>
                  {/* End Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      END DATE <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      className={`w-full border ${showError('endDate') ? 'border-red-400' : 'border-gray-300'} rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                      value={endDate}
                      onChange={e => { setEndDate(e.target.value); setTouched(t => ({...t, endDate: true})); }}
                      onBlur={() => setTouched(t => ({...t, endDate: true}))}
                      min={startDate || new Date().toISOString().split('T')[0]}
                      required
                      disabled={isHalfDay}
                    />
                    {showError('endDate') && (
                      <p className="text-xs text-red-600 mt-1">End date is required.</p>
                    )}
                    {isHalfDay && (
                      <p className="text-xs text-gray-500 mt-1">End date is automatically set for half-day leave</p>
                    )}
                  </div>
                  {/* Leave Summary */}
                  {totalDays > 0 && (
                    <div className="col-span-2">
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <span className="text-blue-900 font-semibold text-lg">
                              {totalDays} {totalDays === 1 ? 'Day' : 'Days'} Requested
                            </span>
                            {selectedLeaveType && (
                              <p className="text-blue-700 text-sm mt-1">
                                {selectedLeaveType.label}
                                {selectedLeaveType.maxDays && ` (Limit: ${selectedLeaveType.maxDays} days)`}
                              </p>
                            )}
                          </div>
                          {availableDays !== null && (
                            <div className="text-right">
                              <span className={`font-semibold text-lg ${availableDays >= totalDays ? 'text-green-700' : 'text-red-700'}`}>
                                {availableDays} Available
                              </span>
                              <p className="text-sm text-gray-600">in this category</p>
                            </div>
                          )}
                        </div>
                        {availableDays !== null && totalDays > availableDays && (
                          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                            <div className="flex items-center">
                              <X className="h-4 w-4 text-red-500 mr-2" />
                              <span className="text-red-700 text-sm font-medium">
                                Insufficient leave balance. Request exceeds available days by {totalDays - availableDays}.
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  {/* Reason */}
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      REASON FOR LEAVE <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      className={`w-full border ${showError('reason') ? 'border-red-400' : 'border-gray-300'} rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none`}
                      value={reason}
                      onChange={e => { setReason(e.target.value); setTouched(t => ({...t, reason: true})); }}
                      onBlur={() => setTouched(t => ({...t, reason: true}))}
                      rows={3}
                      required
                      placeholder="Please provide a detailed reason for the leave request..."
                      maxLength={500}
                    />
                    <div className="flex justify-between items-center mt-1">
                      <p className="text-xs text-gray-500">{reason.length}/500 characters</p>
                      {showError('reason') && (
                        <p className="text-xs text-red-600">Reason is required.</p>
                      )}
                    </div>
                  </div>
                  {/* Work Coverage */}
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      WORK COVERAGE ARRANGEMENTS
                    </label>
                    <textarea
                      className="w-full border border-gray-300 rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                      value={workCoverage}
                      onChange={e => setWorkCoverage(e.target.value)}
                      rows={2}
                      placeholder="Describe how the work will be covered during the absence..."
                      maxLength={300}
                    />
                    <p className="text-xs text-gray-500 mt-1">{workCoverage.length}/300 characters</p>
                  </div>
                  {/* Emergency Contact */}
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      EMERGENCY CONTACT INFORMATION
                    </label>
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded-lg px-3 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      value={emergencyContact}
                      onChange={e => setEmergencyContact(e.target.value)}
                      placeholder="Phone number or email for emergency contact..."
                    />
                  </div>
                </div>
              </div>
              {/* Supporting Documents Section */}
              <div className="bg-white rounded-xl p-6 shadow-sm border mt-8">
                <h4 className="font-semibold text-gray-900 mb-6 flex items-center text-lg">
                  <Upload className="h-5 w-5 mr-2 text-blue-600" />
                  Supporting Documents
                </h4>
                <div 
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                    onChange={e => e.target.files && handleFileUpload(e.target.files)}
                    className="hidden"
                    id="fileUpload"
                  />
                  <label htmlFor="fileUpload" className="cursor-pointer">
                    <div className="flex flex-col items-center">
                      <Upload className="h-8 w-8 text-gray-400 mb-3" />
                      <p className="text-sm text-gray-600 mb-1">
                        <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">
                        PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB per file)
                      </p>
                    </div>
                  </label>
                </div>
                {/* File List */}
                {attachments.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <h6 className="text-sm font-medium text-gray-700">Uploaded Files ({attachments.length})</h6>
                    {attachments.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg border">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <File className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900 truncate max-w-xs" title={file.name}>
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeFile(index)}
                          className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                          title="Remove file"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </section>
          </div>
          {/* Notification */}
          {notification && (
            <div className={`mt-4 p-4 rounded-lg border ${
              notification.type === 'success' 
                ? 'bg-green-50 text-green-800 border-green-200' 
                : 'bg-red-50 text-red-800 border-red-200'
            }`}>
              <div className="flex items-center">
                {notification.type === 'success' ? (
                  <Check className="h-5 w-5 text-green-600 mr-2" />
                ) : (
                  <X className="h-5 w-5 text-red-600 mr-2" />
                )}
                <span className="font-medium">{notification.message}</span>
              </div>
            </div>
          )}
        </div>
        {/* Footer */}
        <div className="sticky bottom-0 bg-gray-50 px-8 py-6 flex justify-end space-x-3 border-t border-gray-200 rounded-b-2xl shadow-inner z-10">
          <button
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors font-medium flex items-center"
            onClick={onClose}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" /> Cancel
          </button>
          <button
            className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium shadow-sm flex items-center"
            onClick={onSubmit}
            disabled={isSubmitting || !selectedEmployee || !leaveType || !startDate || !endDate || !reason}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </div>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" /> Create Leave Request
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeaveRequestsTable;